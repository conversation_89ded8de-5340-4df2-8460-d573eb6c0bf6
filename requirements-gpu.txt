# ------------------------------
# GPU版本依赖文件 - 兼容CUDA 12.8
# 使用稳定版本确保向下兼容性
# ------------------------------

# PyTorch GPU (CUDA 12.1 - 兼容CUDA 12.8)
--extra-index-url https://download.pytorch.org/whl/cu121
torch==2.1.2+cu121
torchvision==0.16.2+cu121
torchaudio==2.1.2+cu121

# ------------------------------
# Web & API Framework
# ------------------------------
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# ------------------------------
# LLM & RAG (稳定版本)
# ------------------------------
langchain==0.0.350
llama-index==0.9.30
transformers==4.36.2
sentence-transformers==2.2.2
accelerate==0.25.0

# ------------------------------
# Vector Store & GPU加速
# ------------------------------
pymilvus==2.3.4
numpy==1.24.4

# ------------------------------
# Data Processing
# ------------------------------
pandas==2.1.4
scikit-learn==1.3.2
nltk==3.8.1
beautifulsoup4==4.12.2

# ------------------------------
# Database & ORM
# ------------------------------
psycopg2-binary==2.9.9
sqlalchemy==2.0.23
alembic==1.16.4
redis==5.0.1
hiredis==2.2.3

# ------------------------------
# File Processing
# ------------------------------
pypdf==3.17.4
python-docx==1.1.0
python-multipart==0.0.6
python-dotenv==1.0.0

# ------------------------------
# Utilities
# ------------------------------
loguru==0.7.2
httpx==0.25.2
requests==2.31.0
aiofiles==23.2.0

# ------------------------------
# Dev & Test Tools
# ------------------------------
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0
prometheus-client==0.19.0

# ------------------------------
# GPU优化包 (可选)
# ------------------------------
# xformers==0.0.23  # 内存优化，如需要请取消注释
# cupy-cuda12x==12.3.0  # CuPy GPU加速，如需要请取消注释
